
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/LeaveDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'dae83aDtN5Oe6VqQSxT8XBO', 'LeaveDialogController');
// scripts/hall/LeaveDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LeaveDialogController = /** @class */ (function (_super) {
    __extends(LeaveDialogController, _super);
    function LeaveDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.cancelBtn = null;
        _this.leaveBtn = null;
        _this.tipContent = null;
        _this.type = 0; //0是完全退出这个游戏 1是退出本局游戏
        _this.roomId = 0; // 房间ID，用于退出时发送给服务器
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    LeaveDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //cancel 按钮点击事件
        Tools_1.Tools.yellowButton(this.cancelBtn, function () {
            _this.hide();
        });
        //leave 按钮点击事件
        Tools_1.Tools.redButton(this.leaveBtn, function () {
            if (_this.type === 0) {
                GameMgr_1.GameMgr.H5SDK.CloseWebView(); //这个是退出游戏的
            }
            else {
                //这个是退出本局游戏的
                _this.hide();
                //退出当前房间游戏
                var leaveRoomData = { 'isConfirmLeave': true };
                if (_this.roomId > 0) {
                    leaveRoomData.roomId = _this.roomId;
                    cc.log("\uD83D\uDEAA \u9000\u51FA\u5173\u5361\u6E38\u620F\u623F\u95F4: " + _this.roomId);
                }
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLeaveRoom, leaveRoomData);
            }
        });
    };
    LeaveDialogController.prototype.show = function (type, backCallback, roomId) {
        if (roomId === void 0) { roomId = 0; }
        this.type = type;
        this.roomId = roomId;
        this.backCallback = backCallback;
        if (type === 0) {
            this.tipContent.string = window.getLocalizedStr('ExitApplication'); //显示完全退出的文案退出
        }
        else {
            this.tipContent.string = window.getLocalizedStr('QuitTheGame'); //显示退出本局游戏的文案
        }
        cc.log("\uD83D\uDD14 LeaveDialogController.show - type: " + type + ", roomId: " + roomId);
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    LeaveDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "cancelBtn", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "leaveBtn", void 0);
    __decorate([
        property(cc.Label)
    ], LeaveDialogController.prototype, "tipContent", void 0);
    LeaveDialogController = __decorate([
        ccclass
    ], LeaveDialogController);
    return LeaveDialogController;
}(cc.Component));
exports.default = LeaveDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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