// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html


import InfoItemController from "../pfb/InfoItemController";
import InfoItemOneController from "../pfb/InfoItemOneController";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;


//游戏道具介绍页面
@ccclass
export default class InfoDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    boardBtnClose: cc.Node = null
    @property(cc.Node)
    contentLay: cc.Node = null

    // 新增：单机规则和联机规则文字按钮
    @property(cc.Node)
    danjiLabel: cc.Node = null
    @property(cc.Node)
    lianjiLabel: cc.Node = null

    // 新增：可移动的视觉效果按钮（不需要点击）
    @property(cc.Node)
    switchButton: cc.Node = null

    // 新增：单机和联机规则的ScrollView
    @property(cc.Node)
    danjiScrollView: cc.Node = null
    @property(cc.Node)
    duorenScrollView: cc.Node = null
    @property(cc.Prefab)
    infoItem: cc.Prefab = null;
    @property(cc.Prefab)
    infoItem1: cc.Prefab = null;

    @property(cc.Prefab)
    infoImage1: cc.Prefab = null
    @property(cc.Prefab)
    infoImage2: cc.Prefab = null
    @property(cc.Prefab)
    infoImage3: cc.Prefab = null
    @property(cc.Prefab)
    infoImage4: cc.Prefab = null
    @property(cc.Prefab)
    infoImage5: cc.Prefab = null
    @property(cc.Prefab)
    infoImage6: cc.Prefab = null
    @property(cc.Prefab)
    infoImage7: cc.Prefab = null
    @property(cc.Prefab)
    infoImage8: cc.Prefab = null
    @property(cc.Prefab)
    infoImage9: cc.Prefab = null
    @property(cc.Prefab)
    infoImage10: cc.Prefab = null
    @property(cc.Prefab)
    infoImage11: cc.Prefab = null

    titleList: string[] = []//title 的列表
    tipsList: string[] = []
    generationMethodList: string[] = []
    permanentList: string[] = []
    randomList: string[] = []
    chainsList: string[] = []
    iceList: string[] = []
    scoringDetails: string[] = []

    infoImageList: cc.Prefab[] = []


    backCallback: Function = null //隐藏弹窗的回调

    // 新增：当前选中的规则类型（0: 单机规则, 1: 联机规则）
    private currentRuleType: number = 0

    // 新增：动画持续时间
    private animationDuration: number = 0.3

    onLoad() {

    //     this.infoImageList = [
    //         this.infoImage3,
    //         this.infoImage4,
    //         this.infoImage5,
    //         this.infoImage6,
    //         this.infoImage7,
    //         this.infoImage8,
    //         this.infoImage9,
    //         this.infoImage10,
    //         this.infoImage11,
    //     ]


    //     this.titleList = [
    //         window.getLocalizedStr('Tips'),
    //         window.getLocalizedStr('Generation_Method'),
    //         window.getLocalizedStr('Permanent_Task'),
    //         window.getLocalizedStr('Random_Task'),
    //         window.getLocalizedStr('Chains'),
    //         window.getLocalizedStr('Ice_Blocks'),
    //         window.getLocalizedStr('Scoring_Details'),
    //     ]//title 的列表
    //     this. tipsList = [
    //         window.getLocalizedStr('Tips1'),
    //         window.getLocalizedStr('Tips2'),
    //         window.getLocalizedStr('Tips3'),
    //         window.getLocalizedStr('Tips4'),
    //         window.getLocalizedStr('Tips5'),
    //     ]
    //     this.generationMethodList = [
    //         window.getLocalizedStr('Generation_Method1'),
    //         window.getLocalizedStr('Generation_Method2'),
    //         window.getLocalizedStr('Generation_Method3'),
    //         window.getLocalizedStr('Generation_Method4'),
    //         window.getLocalizedStr('Generation_Method5'),
    //         window.getLocalizedStr('Generation_Method6'),
    //         window.getLocalizedStr('Generation_Method7'),
    //         window.getLocalizedStr('Generation_Method8'),
    //         window.getLocalizedStr('Generation_Method9'),
    //     ]
    //     this.permanentList = [
    //         window.getLocalizedStr('Permanent_Task1'),
    //     ]
    //     this.randomList = [
    //         window.getLocalizedStr('Random_Task1'),
    //         window.getLocalizedStr('Random_Task2'),
    //         window.getLocalizedStr('Random_Task3'),
    //         window.getLocalizedStr('Random_Task4'),
    //         window.getLocalizedStr('Random_Task5'),
    //         window.getLocalizedStr('Random_Task6'),
    //         window.getLocalizedStr('Random_Task7'),
    //     ]
    //     this.chainsList = [
    //         window.getLocalizedStr('Chains1'),
    //         window.getLocalizedStr('Chains2'),
    //         window.getLocalizedStr('Chains3'),
    //     ]
    //     this.iceList = [
    //         window.getLocalizedStr('Ice_Blocks1'),
    //         window.getLocalizedStr('Ice_Blocks2'),
    //         window.getLocalizedStr('Ice_Blocks3'),
    //     ]
    //     this.scoringDetails = [
    //         window.getLocalizedStr('Scoring_Details1'),
    //         window.getLocalizedStr('Scoring_Details2'),
    //         window.getLocalizedStr('Scoring_Details3'),
    //         window.getLocalizedStr('Scoring_Details4'),
    //         window.getLocalizedStr('Scoring_Details5'),
    //         window.getLocalizedStr('Scoring_Details6'),
    //         window.getLocalizedStr('Scoring_Details7'),
    //         window.getLocalizedStr('Scoring_Details8'),
    //         window.getLocalizedStr('Scoring_Details9'),
    //         window.getLocalizedStr('Scoring_Details10'),
    //         window.getLocalizedStr('Scoring_Details11'),
    //     ]



    }

    start() {

        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {
            this.hide()
        });

        // 新增：设置单机规则文字点击事件
        if (this.danjiLabel) {
            Tools.setTouchEvent(this.danjiLabel, () => {
                this.switchToRuleType(0); // 切换到单机规则
            });
        }

        // 新增：设置联机规则文字点击事件
        if (this.lianjiLabel) {
            Tools.setTouchEvent(this.lianjiLabel, () => {
                this.switchToRuleType(1); // 切换到联机规则
            });
        }

        // 新增：初始化显示状态（默认显示单机规则）
        this.initializeRuleDisplay();

    //     this.contentLay.removeAllChildren()

    //     this.getTitleNode(this.titleList[0])
    //     this.tipsList.forEach((title, index) => {
    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
    //         infoItemOneController.setData(title)
    //         this.contentLay.addChild(infoItem)
    //     })

    //     this.getTitleNode(this.titleList[1])
    //     this.generationMethodList.forEach((title, index) => {
    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
    //         infoItemOneController.setData(title)

    //         let infoImg = cc.instantiate(this.infoImageList[index])
    //         infoItemOneController.setimgNode(infoImg)

    //         this.contentLay.addChild(infoItem)
    //     })
    //     this.getTitleNode(this.titleList[2])
    //     this.permanentList.forEach((title, index) => {
    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
    //         infoItemOneController.setData(title)
    //         this.contentLay.addChild(infoItem)
    //     })
    //     let infoImg1 = cc.instantiate(this.infoImage1)
    //     this.contentLay.addChild(infoImg1)


    //     this.getTitleNode(this.titleList[3])
    //     this.randomList.forEach((title, index) => {
    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
    //         infoItemOneController.setData(title)
    //         this.contentLay.addChild(infoItem)
    //     })
    //     let infoImg2 = cc.instantiate(this.infoImage2)
    //     this.contentLay.addChild(infoImg2)

    //     this.getTitleNode(this.titleList[4])
    //     this.chainsList.forEach((title, index) => {
    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
    //         infoItemOneController.setData(title)
    //         this.contentLay.addChild(infoItem)
    //     })
    //     this.getTitleNode(this.titleList[5])
    //     this.iceList.forEach((title, index) => {
    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
    //         infoItemOneController.setData(title)
    //         this.contentLay.addChild(infoItem)
    //     })
    //     this.getTitleNode(this.titleList[6])
    //     this.scoringDetails.forEach((title, index) => {
    //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
    //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
    //         infoItemOneController.setData(title)
    //         this.contentLay.addChild(infoItem)
    //     })
    // }


    // getTitleNode(title: string) {
    //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
    //     let infoItemController = infoItem.getComponent(InfoItemController)
    //     infoItemController.setContent(title)
    //     this.contentLay.addChild(infoItem)
    // }
    }

    /**
     * 新增：初始化规则显示状态
     */
    private initializeRuleDisplay() {
        if (this.danjiScrollView && this.duorenScrollView) {
            // 默认显示单机规则，隐藏联机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
            this.currentRuleType = 0;

            // 设置按钮初始位置（左边位置）
            if (this.switchButton) {
                this.switchButton.position = cc.v3(-150, -2, 0);
            }
        }
    }

    /**
     * 新增：切换到指定规则类型
     * @param ruleType 0: 单机规则, 1: 联机规则
     */
    private switchToRuleType(ruleType: number) {
        if (this.currentRuleType === ruleType) {
       
            return; // 如果已经是当前类型，不需要切换
        }

        this.currentRuleType = ruleType;

      

        // 移动按钮位置（视觉效果）
        this.moveButtonToPosition();

        // 切换ScrollView显示
        this.switchScrollViewDisplay();
    }

    /**
     * 新增：移动按钮到指定位置
     */
    private moveButtonToPosition() {
        if (!this.switchButton) {
            return;
        }

        // 按钮位置：左边（-150，-2）右边（142，-2）
        const leftPosition = cc.v3(-150, -2, 0);
        const rightPosition = cc.v3(142, -2, 0);

        const targetPosition = this.currentRuleType === 0 ? leftPosition : rightPosition;

        // 使用动画移动按钮
        cc.tween(this.switchButton)
            .to(0.3, { position: targetPosition }, { easing: 'quartOut' })
            .start();
    }

    /**
     * 新增：切换ScrollView显示
     */
    private switchScrollViewDisplay() {
        if (!this.danjiScrollView || !this.duorenScrollView) {
            return;
        }

        if (this.currentRuleType === 0) {
            // 显示单机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
        } else {
            // 显示联机规则
            this.danjiScrollView.active = false;
            this.duorenScrollView.active = true;
        }
    }



    show(backCallback: Function) {
        this.backCallback = backCallback
        this.node.active = true
        this.boardBg.scale = 0
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }
    hide() {
        if (this.backCallback) {
            this.backCallback()
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
            })
            .start();
    }

    // update (dt) {}
}