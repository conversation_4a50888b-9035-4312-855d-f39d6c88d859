// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { LevelStatus } from "./LevelSelectController";

const { ccclass, property } = cc._decorator;

/**
 * 关卡选择使用示例
 * 这个脚本展示了如何快速创建一个关卡选择界面
 */
@ccclass
export default class LevelSelectExample extends cc.Component {

    // UI节点
    private scrollView: cc.ScrollView = null;
    private content: cc.Node = null;
    private infoLabel: cc.Label = null;

    // 关卡数据
    private levelDataList: any[] = [];
    private currentSelectedLevel: number = 1;
    private totalLevels: number = 30;
    private levelItemWidth: number = 150;

    // 关卡节点列表
    private levelNodes: cc.Node[] = [];

    onLoad() {
        this.createUI();
        this.initLevelData();
        this.createLevelSelectUI();
    }

    start() {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    }

    /**
     * 创建UI结构
     */
    private createUI() {
        // 创建背景
        const bg = new cc.Node("Background");
        bg.addComponent(cc.Sprite);
        bg.setContentSize(750, 1334);
        bg.color = cc.Color.BLACK;
        bg.parent = this.node;

        // 创建标题
        const titleNode = new cc.Node("Title");
        const titleLabel = titleNode.addComponent(cc.Label);
        titleLabel.string = "选择关卡";
        titleLabel.fontSize = 36;
        titleLabel.node.color = cc.Color.WHITE;
        titleNode.setPosition(0, 500);
        titleNode.parent = this.node;

        // 创建ScrollView
        const scrollViewNode = new cc.Node("ScrollView");
        this.scrollView = scrollViewNode.addComponent(cc.ScrollView);
        scrollViewNode.setContentSize(650, 150);
        scrollViewNode.setPosition(0, 0);
        scrollViewNode.parent = this.node;

        // 创建Viewport
        const viewport = new cc.Node("Viewport");
        viewport.addComponent(cc.Mask);
        viewport.setContentSize(650, 150);
        viewport.parent = scrollViewNode;

        // 创建Content
        this.content = new cc.Node("Content");
        this.content.setContentSize(650, 150);
        this.content.parent = viewport;

        // 设置ScrollView属性
        this.scrollView.content = this.content;
        this.scrollView.horizontal = true;
        this.scrollView.vertical = false;
        this.scrollView.inertia = true;
        this.scrollView.elastic = true;

        // 修复Scrollbar问题
        this.scrollView.horizontalScrollBar = null;
        this.scrollView.verticalScrollBar = null;

        // 创建信息标签
        const infoNode = new cc.Node("InfoLabel");
        this.infoLabel = infoNode.addComponent(cc.Label);
        this.infoLabel.string = "当前选中: 关卡1";
        this.infoLabel.fontSize = 24;
        this.infoLabel.node.color = cc.Color.WHITE;
        infoNode.setPosition(0, -200);
        infoNode.parent = this.node;

        // 创建测试按钮
        this.createTestButtons();
    }

    /**
     * 创建测试按钮
     */
    private createTestButtons() {
        // 完成关卡按钮
        const completeBtn = this.createButton("完成当前关卡", cc.v2(-150, -300), () => {
            this.completeCurrentLevel();
        });

        // 重置按钮
        const resetBtn = this.createButton("重置关卡", cc.v2(0, -300), () => {
            this.resetLevels();
        });

        // 随机进度按钮
        const randomBtn = this.createButton("随机进度", cc.v2(150, -300), () => {
            this.setRandomProgress();
        });
    }

    /**
     * 创建按钮
     */
    private createButton(text: string, position: cc.Vec2, callback: Function): cc.Node {
        const btnNode = new cc.Node("Button");
        const button = btnNode.addComponent(cc.Button);
        const sprite = btnNode.addComponent(cc.Sprite);
        
        // 设置按钮外观
        btnNode.setContentSize(120, 40);
        btnNode.color = cc.Color.BLUE;
        btnNode.setPosition(position);
        btnNode.parent = this.node;

        // 添加文字
        const labelNode = new cc.Node("Label");
        const label = labelNode.addComponent(cc.Label);
        label.string = text;
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = btnNode;

        // 设置点击事件
        btnNode.on('click', callback, this);

        return btnNode;
    }

    /**
     * 初始化关卡数据
     */
    private initLevelData() {
        this.levelDataList = [];
        for (let i = 1; i <= this.totalLevels; i++) {
            let status: LevelStatus;
            if (i === 1) {
                status = LevelStatus.CURRENT;
            } else if (i <= 3) {
                status = LevelStatus.COMPLETED;
            } else {
                status = LevelStatus.LOCKED;
            }

            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    }

    /**
     * 创建关卡选择UI
     */
    private createLevelSelectUI() {
        if (!this.content) return;

        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];

        // 计算总宽度
        const totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;

        for (let i = 0; i < this.totalLevels; i++) {
            const levelData = this.levelDataList[i];
            
            // 创建关卡节点
            const levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);

            // 设置位置
            const posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);

            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                const lineNode = this.createLineNode();
                this.content.addChild(lineNode);
                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);
            }
        }
    }

    /**
     * 创建关卡节点
     */
    private createLevelNode(levelData: any): cc.Node {
        const node = new cc.Node(`Level_${levelData.levelNumber}`);
        
        // 添加Sprite组件
        const sprite = node.addComponent(cc.Sprite);
        
        // 添加Label组件显示关卡数字
        const labelNode = new cc.Node("LevelLabel");
        const label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        label.fontSize = 20;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = node;

        // 添加Button组件
        const button = node.addComponent(cc.Button);
        button.target = node;

        // 设置点击事件
        node.on('click', () => {
            this.onLevelClicked(levelData.levelNumber);
        }, this);

        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);

        return node;
    }

    /**
     * 创建连接线节点
     */
    private createLineNode(): cc.Node {
        const node = new cc.Node("Line");
        const sprite = node.addComponent(cc.Sprite);
        node.setContentSize(6, 6);
        node.color = cc.Color.WHITE;

        return node;
    }

    /**
     * 更新关卡节点外观
     */
    private updateLevelNodeAppearance(node: cc.Node, levelData: any, isSelected: boolean) {
        let size = cc.size(46, 46);
        let color = cc.Color.GRAY;

        // 根据状态确定颜色和大小
        if (isSelected) {
            size = cc.size(86, 86);
        }

        switch (levelData.status) {
            case LevelStatus.LOCKED:
                color = cc.Color.GRAY;
                break;
            case LevelStatus.CURRENT:
                color = cc.Color.YELLOW;
                break;
            case LevelStatus.COMPLETED:
                color = cc.Color.GREEN;
                break;
        }

        // 设置节点大小和颜色
        node.setContentSize(size);
        node.color = color;
    }

    /**
     * 更新所有关卡显示
     */
    private updateAllLevelsDisplay() {
        for (let i = 0; i < this.levelNodes.length; i++) {
            const node = this.levelNodes[i];
            const levelData = this.levelDataList[i];
            const isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    }

    /**
     * 滚动到指定关卡
     */
    private scrollToLevel(levelNumber: number) {
        if (levelNumber < 1 || levelNumber > this.totalLevels) return;

        const targetIndex = levelNumber - 1;
        const contentWidth = this.content.width;
        const scrollViewWidth = this.scrollView.node.width;
        
        const targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        const clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    }

    /**
     * 关卡点击事件处理
     */
    private onLevelClicked(levelNumber: number) {
        const levelData = this.levelDataList[levelNumber - 1];
        if (levelData.status === LevelStatus.LOCKED) {
        
            return;
        }

        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
        
       
    }

    /**
     * 更新信息显示
     */
    private updateInfoDisplay() {
        if (this.infoLabel) {
            const levelData = this.levelDataList[this.currentSelectedLevel - 1];
            let statusText = "";
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = `当前选中: 关卡${this.currentSelectedLevel} (${statusText})`;
        }
    }

    /**
     * 完成当前关卡
     */
    private completeCurrentLevel() {
        const levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelStatus.CURRENT) {
            levelData.status = LevelStatus.COMPLETED;
            
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                const nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelStatus.LOCKED) {
                    nextLevelData.status = LevelStatus.CURRENT;
                }
            }
            
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    }

    /**
     * 重置关卡
     */
    private resetLevels() {
        for (let i = 0; i < this.levelDataList.length; i++) {
            if (i === 0) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
            } else {
                this.levelDataList[i].status = LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = 1;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(1);
        this.updateInfoDisplay();
    }

    /**
     * 设置随机进度
     */
    private setRandomProgress() {
        const completedLevels = Math.floor(Math.random() * 10) + 1;
        const currentLevel = Math.min(completedLevels + 1, this.totalLevels);

        for (let i = 0; i < this.levelDataList.length; i++) {
            if (i < completedLevels) {
                this.levelDataList[i].status = LevelStatus.COMPLETED;
            } else if (i === completedLevels) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
            } else {
                this.levelDataList[i].status = LevelStatus.LOCKED;
            }
        }

        this.currentSelectedLevel = currentLevel;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(currentLevel);
        this.updateInfoDisplay();
    }
}
